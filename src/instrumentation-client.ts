// This file is normally used for setting up analytics and other
// services that require one-time initialization on the client.

import { retrieveLaunchParams, isTMA } from "@telegram-apps/sdk-react";
import { init } from "./core/init";
import { mockEnv } from "./utils/mock-env";

async function initializeApp() {
  // First, set up mock environment if needed
  await mockEnv();

  // Check if we're in a Telegram Mini App environment
  const isInTMA = await isTMA("complete");

  let platform = "unknown";
  let debug = process.env.NODE_ENV === "development";

  if (isInTMA) {
    try {
      const launchParams = retrieveLaunchParams();
      platform = launchParams.tgWebAppPlatform || "unknown";
      debug =
        (launchParams.tgWebAppStartParam || "").includes("debug") || debug;
    } catch (e) {
      console.warn("Failed to retrieve launch params in TMA environment:", e);
    }
  } else {
    console.log(
      "Not in Telegram Mini App environment, using development defaults"
    );
  }

  // Configure all application dependencies
  init({
    debug,
    eruda: debug && ["ios", "android"].includes(platform),
    mockForMacOS: platform === "macos",
  });
}

// Initialize the app
initializeApp().catch(console.error);
